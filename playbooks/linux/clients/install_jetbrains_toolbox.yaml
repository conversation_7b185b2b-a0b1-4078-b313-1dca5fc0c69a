---
# JetBrains Toolbox Installation Playbook
#
# This playbook installs JetBrains Toolbox for Linux clients with:
# - Required system dependencies (libfuse2, libxi6, etc.)
# - Latest JetBrains Toolbox from official API
# - Installation for user 'krizzo' by default
# - Desktop integration and PATH setup
# - Automatic updates capability
#
# Usage:
#   ansible-playbook -i hosts.yaml install_jetbrains_toolbox.yaml -e "hosts=linux_clients" -u aule -b
#
# Custom user installation:
#   ansible-playbook -i hosts.yaml install_jetbrains_toolbox.yaml -e "hosts=linux_clients jetbrains_toolbox_user=myuser" -u aule -b
#
# Tags available:
#   --tags jetbrains                    # Install everything
#   --tags dependencies                 # Only install system dependencies
#   --tags download                     # Only download and install Toolbox
#   --tags desktop                      # Only setup desktop integration

- name: Install JetBrains Toolbox
  hosts: "{{ hosts | default('linux_clients') }}"
  gather_facts: true
  become: true
  remote_user: aule

  vars:
    # Default user for JetBrains Toolbox installation
    jetbrains_toolbox_user: "{{ jetbrains_toolbox_user | default('krizzo') }}"
    
    # Required packages for JetBrains Toolbox
    jetbrains_dependencies:
      debian:
        - libfuse2
        - libxi6
        - libxrender1
        - libxtst6
        - mesa-utils
        - libfontconfig1
        - libgtk-3-bin
        - tar
        - dbus-user-session
        - curl
        - wget
      rhel:
        - fuse
        - libXi
        - libXrender
        - libXtst
        - mesa-utils
        - fontconfig
        - gtk3
        - tar
        - curl
        - wget

  tasks:
    - name: Ensure Ansible temporary directory exists
      ansible.builtin.file:
        path: /tmp/.ansible-{{ ansible_user | default('ansible') }}
        state: directory
        mode: '0700'
        owner: "{{ ansible_user | default('ansible') }}"
      tags: ['setup']

    - name: Display system information
      ansible.builtin.debug:
        msg: |
          Installing JetBrains Toolbox:
          - OS Family: {{ ansible_facts['os_family'] }}
          - Distribution: {{ ansible_facts['distribution'] }}
          - Distribution Version: {{ ansible_facts['distribution_version'] }}
          - Architecture: {{ ansible_facts['architecture'] }}
          - Target User: {{ jetbrains_toolbox_user }}
      tags: ['info']

    - name: Set OS family facts
      ansible.builtin.set_fact:
        is_debian_family: "{{ ansible_facts['os_family'] | lower == 'debian' }}"
        is_rhel_family: "{{ ansible_facts['os_family'] | lower in ['redhat', 'rocky', 'centos', 'fedora'] }}"
      tags: ['always']

    - name: Validate supported OS family
      ansible.builtin.fail:
        msg: "Unsupported OS family: {{ ansible_facts['os_family'] }}. This playbook supports Debian and RHEL-based systems only."
      when: not (is_debian_family or is_rhel_family)
      tags: ['validation']

    - name: Validate architecture
      ansible.builtin.fail:
        msg: "Unsupported architecture: {{ ansible_facts['architecture'] }}. JetBrains Toolbox requires x86_64 or arm64."
      when: ansible_facts['architecture'] not in ['x86_64', 'amd64', 'aarch64', 'arm64']
      tags: ['validation']

    # Phase 1: Install Dependencies
    - name: Phase 1 - Install dependencies on Debian-based systems
      block:
        - name: Update apt cache
          ansible.builtin.apt:
            update_cache: true
            cache_valid_time: 3600
          tags: ['cache']

        - name: Install JetBrains Toolbox dependencies (Debian)
          ansible.builtin.apt:
            name: "{{ jetbrains_dependencies.debian }}"
            state: present
            update_cache: false
          tags: ['dependencies']
      when: is_debian_family
      tags: ['dependencies', 'debian']

    - name: Phase 1 - Install dependencies on RHEL-based systems
      block:
        - name: Update dnf cache
          ansible.builtin.dnf:
            update_cache: true
          tags: ['cache']

        - name: Install JetBrains Toolbox dependencies (RHEL)
          ansible.builtin.dnf:
            name: "{{ jetbrains_dependencies.rhel }}"
            state: present
            update_cache: false
          tags: ['dependencies']
      when: is_rhel_family
      tags: ['dependencies', 'rhel']

    # Phase 2: Install JetBrains Toolbox
    - name: Phase 2 - Install JetBrains Toolbox
      ansible.builtin.include_tasks: tasks/install_jetbrains_toolbox.yml
      tags: ['jetbrains', 'install']

    # Phase 3: Verification and Completion
    - name: Phase 3 - Verify target user exists
      ansible.builtin.getent:
        database: passwd
        key: "{{ jetbrains_toolbox_user }}"
      register: final_user_check
      tags: ['verification']

    - name: Phase 3 - Get user home directory
      ansible.builtin.set_fact:
        user_home: "{{ final_user_check.ansible_facts.getent_passwd[jetbrains_toolbox_user][4] }}"
      tags: ['verification']

    - name: Phase 3 - Verify JetBrains Toolbox installation
      ansible.builtin.stat:
        path: "{{ user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox"
      register: final_verification
      tags: ['verification']

    - name: Phase 3 - Installation complete
      ansible.builtin.debug:
        msg: |
          JetBrains Toolbox installation completed successfully!

          Installation Summary:
          - Target User: {{ jetbrains_toolbox_user }}
          - User Home: {{ user_home }}
          - Binary Location: {{ user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox
          - Desktop Entry: {{ user_home }}/.local/share/applications/jetbrains-toolbox.desktop
          - PATH Symlink: {{ user_home }}/.local/bin/jetbrains-toolbox

          Next Steps for {{ jetbrains_toolbox_user }}:
          1. Login as {{ jetbrains_toolbox_user }}: su - {{ jetbrains_toolbox_user }}
          2. Ensure ~/.local/bin is in PATH (add to ~/.bashrc or ~/.zshrc if needed):
             export PATH="$HOME/.local/bin:$PATH"
          3. Launch JetBrains Toolbox:
             - Command line: jetbrains-toolbox
             - Desktop menu: Search for "JetBrains Toolbox"
             - Direct path: {{ user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox
          4. Sign in with your JetBrains account
          5. Install desired IDEs (IntelliJ IDEA, PyCharm, WebStorm, etc.)

          Features:
          - Automatic updates for Toolbox and installed IDEs
          - Centralized license management
          - Easy IDE version management
          - Project-specific IDE configurations

          Troubleshooting:
          - If desktop entry doesn't appear, run: update-desktop-database ~/.local/share/applications
          - For FUSE issues on older systems, ensure FUSE is properly configured
          - Check system requirements: https://www.jetbrains.com/help/toolbox-app/toolbox-app-system-requirements.html
      when: final_verification.stat.exists
      tags: ['completion']

    - name: Phase 3 - Installation failed
      ansible.builtin.fail:
        msg: |
          JetBrains Toolbox installation failed!
          
          The binary was not found at: {{ user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox
          
          Please check:
          1. System requirements are met
          2. All dependencies are installed
          3. User {{ jetbrains_toolbox_user }} exists and has proper permissions
          4. Network connectivity for downloading the installer
          
          For manual installation, visit: https://www.jetbrains.com/toolbox-app/
      when: not final_verification.stat.exists
      tags: ['completion']
