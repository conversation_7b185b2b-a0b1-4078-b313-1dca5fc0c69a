---
# Install JetBrains Toolbox for Linux
# Based on: https://www.jetbrains.com/help/toolbox-app/toolbox-app-silent-installation.html#toolbox_linux
#
# This task installs JetBrains Toolbox by:
# - Installing required dependencies (libfuse2, libxi6, etc.)
# - Downloading the latest tarball from JetBrains
# - Extracting and installing to user's home directory
# - Creating desktop entry for easy access
# - Setting up automatic updates

- name: Install JetBrains Toolbox - Check if running on Linux
  ansible.builtin.fail:
    msg: "This task only supports Linux distributions"
  when: ansible_facts['system'] | lower != 'linux'
  tags: ['jetbrains', 'validation']

- name: Install JetBrains Toolbox - Set target user
  ansible.builtin.set_fact:
    jetbrains_user: "{{ jetbrains_toolbox_user | default('krizzo') }}"
  tags: ['jetbrains', 'setup']

- name: Install JetBrains Toolbox - Verify target user exists
  ansible.builtin.getent:
    database: passwd
    key: "{{ jetbrains_user }}"
  register: user_check
  tags: ['jetbrains', 'validation']

- name: Install JetBrains Toolbox - Fail if user doesn't exist
  ansible.builtin.fail:
    msg: "User {{ jetbrains_user }} does not exist. Please create the user first."
  when: user_check.ansible_facts.getent_passwd[jetbrains_user] is not defined
  tags: ['jetbrains', 'validation']

- name: Install JetBrains Toolbox - Get user home directory
  ansible.builtin.set_fact:
    jetbrains_user_home: "{{ user_check.ansible_facts.getent_passwd[jetbrains_user][4] }}"
  tags: ['jetbrains', 'setup']

# Install required dependencies for Debian-based systems
- name: Install JetBrains Toolbox - Install dependencies (Debian/Ubuntu)
  ansible.builtin.apt:
    name:
      - libfuse2
      - libxi6
      - libxrender1
      - libxtst6
      - mesa-utils
      - libfontconfig1
      - libgtk-3-bin
      - tar
      - dbus-user-session
      - curl
      - wget
    state: present
    update_cache: true
  when: ansible_facts['os_family'] | lower == 'debian'
  tags: ['jetbrains', 'dependencies']

# Install required dependencies for RHEL-based systems
- name: Install JetBrains Toolbox - Install dependencies (RHEL/CentOS/Fedora)
  ansible.builtin.dnf:
    name:
      - fuse
      - libXi
      - libXrender
      - libXtst
      - mesa-utils
      - fontconfig
      - gtk3
      - tar
      - curl
      - wget
    state: present
    update_cache: true
  when: ansible_facts['os_family'] | lower in ['redhat', 'rocky', 'centos', 'fedora']
  tags: ['jetbrains', 'dependencies']

- name: Install JetBrains Toolbox - Create temporary download directory
  ansible.builtin.tempfile:
    state: directory
    suffix: jetbrains_toolbox
  register: temp_dir
  tags: ['jetbrains', 'download']

- name: Install JetBrains Toolbox - Get latest download URL
  ansible.builtin.uri:
    url: https://data.services.jetbrains.com/products/releases?code=TBA&latest=true&type=release
    method: GET
    return_content: true
  register: jetbrains_api_response
  tags: ['jetbrains', 'download']

- name: Install JetBrains Toolbox - Parse download URL
  ansible.builtin.set_fact:
    jetbrains_download_url: "{{ jetbrains_api_response.json.TBA[0].downloads.linux.link }}"
    jetbrains_version: "{{ jetbrains_api_response.json.TBA[0].version }}"
    jetbrains_build: "{{ jetbrains_api_response.json.TBA[0].build }}"
  tags: ['jetbrains', 'download']

- name: Install JetBrains Toolbox - Display download information
  ansible.builtin.debug:
    msg: |
      Downloading JetBrains Toolbox:
      Version: {{ jetbrains_version }}
      Build: {{ jetbrains_build }}
      URL: {{ jetbrains_download_url }}
  tags: ['jetbrains', 'info']

- name: Install JetBrains Toolbox - Download tarball
  ansible.builtin.get_url:
    url: "{{ jetbrains_download_url }}"
    dest: "{{ temp_dir.path }}/jetbrains-toolbox.tar.gz"
    mode: '0644'
    timeout: 300
  tags: ['jetbrains', 'download']

- name: Install JetBrains Toolbox - Create installation directory
  ansible.builtin.file:
    path: "{{ jetbrains_user_home }}/.local/share/JetBrains/Toolbox"
    state: directory
    mode: '0755'
    owner: "{{ jetbrains_user }}"
    group: "{{ jetbrains_user }}"
    recurse: true
  tags: ['jetbrains', 'install']

- name: Install JetBrains Toolbox - Extract tarball
  ansible.builtin.unarchive:
    src: "{{ temp_dir.path }}/jetbrains-toolbox.tar.gz"
    dest: "{{ temp_dir.path }}"
    remote_src: true
    owner: "{{ jetbrains_user }}"
    group: "{{ jetbrains_user }}"
  tags: ['jetbrains', 'install']

- name: Install JetBrains Toolbox - Find extracted directory
  ansible.builtin.find:
    paths: "{{ temp_dir.path }}"
    file_type: directory
    patterns: "jetbrains-toolbox-*"
  register: extracted_dirs
  tags: ['jetbrains', 'install']

- name: Install JetBrains Toolbox - Set extracted directory path
  ansible.builtin.set_fact:
    jetbrains_extracted_dir: "{{ extracted_dirs.files[0].path }}"
  when: extracted_dirs.files | length > 0
  tags: ['jetbrains', 'install']

- name: Install JetBrains Toolbox - Copy binary to installation directory
  ansible.builtin.copy:
    src: "{{ jetbrains_extracted_dir }}/jetbrains-toolbox"
    dest: "{{ jetbrains_user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox"
    mode: '0755'
    owner: "{{ jetbrains_user }}"
    group: "{{ jetbrains_user }}"
    remote_src: true
  tags: ['jetbrains', 'install']

- name: Install JetBrains Toolbox - Create bin directory in user PATH
  ansible.builtin.file:
    path: "{{ jetbrains_user_home }}/.local/bin"
    state: directory
    mode: '0755'
    owner: "{{ jetbrains_user }}"
    group: "{{ jetbrains_user }}"
  tags: ['jetbrains', 'install']

- name: Install JetBrains Toolbox - Create symlink in user PATH
  ansible.builtin.file:
    src: "{{ jetbrains_user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox"
    dest: "{{ jetbrains_user_home }}/.local/bin/jetbrains-toolbox"
    state: link
    owner: "{{ jetbrains_user }}"
    group: "{{ jetbrains_user }}"
  tags: ['jetbrains', 'install']

- name: Install JetBrains Toolbox - Create desktop entry directory
  ansible.builtin.file:
    path: "{{ jetbrains_user_home }}/.local/share/applications"
    state: directory
    mode: '0755'
    owner: "{{ jetbrains_user }}"
    group: "{{ jetbrains_user }}"
  tags: ['jetbrains', 'desktop']

- name: Install JetBrains Toolbox - Create desktop entry
  ansible.builtin.copy:
    content: |
      [Desktop Entry]
      Version=1.0
      Type=Application
      Name=JetBrains Toolbox
      Icon=jetbrains-toolbox
      Exec="{{ jetbrains_user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox"
      Comment=JetBrains Toolbox
      Categories=Development;
      Terminal=false
      StartupWMClass=jetbrains-toolbox
      StartupNotify=true
    dest: "{{ jetbrains_user_home }}/.local/share/applications/jetbrains-toolbox.desktop"
    mode: '0644'
    owner: "{{ jetbrains_user }}"
    group: "{{ jetbrains_user }}"
  tags: ['jetbrains', 'desktop']

- name: Install JetBrains Toolbox - Clean up temporary files
  ansible.builtin.file:
    path: "{{ temp_dir.path }}"
    state: absent
  tags: ['jetbrains', 'cleanup']

- name: Install JetBrains Toolbox - Verify installation
  ansible.builtin.stat:
    path: "{{ jetbrains_user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox"
  register: jetbrains_binary_check
  tags: ['jetbrains', 'verification']

- name: Install JetBrains Toolbox - Display installation result
  ansible.builtin.debug:
    msg: |
      JetBrains Toolbox installation completed successfully!
      
      Installation details:
      - Version: {{ jetbrains_version }}
      - Build: {{ jetbrains_build }}
      - User: {{ jetbrains_user }}
      - Installation path: {{ jetbrains_user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox
      - Desktop entry: {{ jetbrains_user_home }}/.local/share/applications/jetbrains-toolbox.desktop
      - Symlink in PATH: {{ jetbrains_user_home }}/.local/bin/jetbrains-toolbox
      
      Next steps for user {{ jetbrains_user }}:
      1. Run: jetbrains-toolbox (if ~/.local/bin is in PATH)
      2. Or run: {{ jetbrains_user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox
      3. Or launch from desktop menu: JetBrains Toolbox
      4. Log in with your JetBrains account to activate licenses
      5. Install desired IDEs through the Toolbox interface
      
      The Toolbox will automatically:
      - Install itself to {{ jetbrains_user_home }}/.local/share/JetBrains/Toolbox/bin
      - Add itself to the desktop menu
      - Handle updates for itself and installed IDEs
  when: jetbrains_binary_check.stat.exists
  tags: ['jetbrains', 'completion']

- name: Install JetBrains Toolbox - Installation failed
  ansible.builtin.fail:
    msg: "JetBrains Toolbox installation failed - binary not found at expected location"
  when: not jetbrains_binary_check.stat.exists
  tags: ['jetbrains', 'verification']
